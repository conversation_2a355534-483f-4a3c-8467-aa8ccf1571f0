{"ast": null, "code": "import createPrefixer from 'inline-style-prefixer/lib/createPrefixer';\nimport staticData from \"./static\";\nvar prefixAll = createPrefixer(staticData);\nexport default prefixAll;", "map": {"version": 3, "names": ["createPrefixer", "staticData", "prefixAll"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/prefixStyles/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport createPrefixer from 'inline-style-prefixer/lib/createPrefixer';\nimport staticData from './static';\nvar prefixAll = createPrefixer(staticData);\nexport default prefixAll;"], "mappings": "AASA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU;AACjB,IAAIC,SAAS,GAAGF,cAAc,CAACC,UAAU,CAAC;AAC1C,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}