{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport Platform from \"../../../exports/Platform\";\nimport FlatList from \"./components/AnimatedFlatList\";\nimport Image from \"./components/AnimatedImage\";\nimport ScrollView from \"./components/AnimatedScrollView\";\nimport SectionList from \"./components/AnimatedSectionList\";\nimport Text from \"./components/AnimatedText\";\nimport View from \"./components/AnimatedView\";\nimport AnimatedMock from \"./AnimatedMock\";\nimport AnimatedImplementation from \"./AnimatedImplementation\";\nvar Animated = Platform.isTesting ? AnimatedMock : AnimatedImplementation;\nexport default _objectSpread({\n  FlatList: FlatList,\n  Image: Image,\n  ScrollView: ScrollView,\n  SectionList: SectionList,\n  Text: Text,\n  View: View\n}, Animated);", "map": {"version": 3, "names": ["_objectSpread", "Platform", "FlatList", "Image", "ScrollView", "SectionList", "Text", "View", "AnimatedMock", "AnimatedImplementation", "Animated", "isTesting"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/Animated/Animated.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport Platform from '../../../exports/Platform';\nimport FlatList from './components/AnimatedFlatList';\nimport Image from './components/AnimatedImage';\nimport ScrollView from './components/AnimatedScrollView';\nimport SectionList from './components/AnimatedSectionList';\nimport Text from './components/AnimatedText';\nimport View from './components/AnimatedView';\nimport AnimatedMock from './AnimatedMock';\nimport AnimatedImplementation from './AnimatedImplementation';\nvar Animated = Platform.isTesting ? AnimatedMock : AnimatedImplementation;\nexport default _objectSpread({\n  FlatList,\n  Image,\n  ScrollView,\n  SectionList,\n  Text,\n  View\n}, Animated);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,sCAAsC;AAWhE,OAAOC,QAAQ;AACf,OAAOC,QAAQ;AACf,OAAOC,KAAK;AACZ,OAAOC,UAAU;AACjB,OAAOC,WAAW;AAClB,OAAOC,IAAI;AACX,OAAOC,IAAI;AACX,OAAOC,YAAY;AACnB,OAAOC,sBAAsB;AAC7B,IAAIC,QAAQ,GAAGT,QAAQ,CAACU,SAAS,GAAGH,YAAY,GAAGC,sBAAsB;AACzE,eAAeT,aAAa,CAAC;EAC3BE,QAAQ,EAARA,QAAQ;EACRC,KAAK,EAALA,KAAK;EACLC,UAAU,EAAVA,UAAU;EACVC,WAAW,EAAXA,WAAW;EACXC,IAAI,EAAJA,IAAI;EACJC,IAAI,EAAJA;AACF,CAAC,EAAEG,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}