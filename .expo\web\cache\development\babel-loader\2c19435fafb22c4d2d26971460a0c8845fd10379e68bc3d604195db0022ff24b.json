{"ast": null, "code": "'use strict';\n\nimport invariant from 'fbjs/lib/invariant';\nexport function get(name) {\n  return null;\n}\nexport function getEnforcing(name) {\n  var module = get(name);\n  invariant(module != null, \"TurboModuleRegistry.getEnforcing(...): '\" + name + \"' could not be found. \" + 'Verify that a module by this name is registered in the native binary.');\n  return module;\n}", "map": {"version": 3, "names": ["invariant", "get", "name", "getEnforcing", "module"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/TurboModule/TurboModuleRegistry.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport invariant from 'fbjs/lib/invariant';\nexport function get(name) {\n  return null;\n}\nexport function getEnforcing(name) {\n  var module = get(name);\n  invariant(module != null, \"TurboModuleRegistry.getEnforcing(...): '\" + name + \"' could not be found. \" + 'Verify that a module by this name is registered in the native binary.');\n  return module;\n}"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,oBAAoB;AAC1C,OAAO,SAASC,GAAGA,CAACC,IAAI,EAAE;EACxB,OAAO,IAAI;AACb;AACA,OAAO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,IAAIE,MAAM,GAAGH,GAAG,CAACC,IAAI,CAAC;EACtBF,SAAS,CAACI,MAAM,IAAI,IAAI,EAAE,0CAA0C,GAAGF,IAAI,GAAG,wBAAwB,GAAG,uEAAuE,CAAC;EACjL,OAAOE,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}