{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport * as React from 'react';\nimport ScrollView from \"../../../../exports/ScrollView\";\nimport createAnimatedComponent from \"../createAnimatedComponent\";\nvar ScrollViewWithEventThrottle = React.forwardRef(function (props, ref) {\n  return React.createElement(ScrollView, _extends({\n    scrollEventThrottle: 0.0001\n  }, props, {\n    ref: ref\n  }));\n});\nexport default createAnimatedComponent(ScrollViewWithEventThrottle);", "map": {"version": 3, "names": ["_extends", "React", "ScrollView", "createAnimatedComponent", "ScrollViewWithEventThrottle", "forwardRef", "props", "ref", "createElement", "scrollEventThrottle"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/Animated/components/AnimatedScrollView.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport * as React from 'react';\nimport ScrollView from '../../../../exports/ScrollView';\nimport createAnimatedComponent from '../createAnimatedComponent';\n/**\n * @see https://github.com/facebook/react-native/commit/b8c8562\n */\nvar ScrollViewWithEventThrottle = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(ScrollView, _extends({\n  scrollEventThrottle: 0.0001\n}, props, {\n  ref: ref\n})));\nexport default createAnimatedComponent(ScrollViewWithEventThrottle);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AAWrD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,OAAOC,uBAAuB;AAI9B,IAAIC,2BAA2B,GAAgBH,KAAK,CAACI,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG;EAAA,OAAkBN,KAAK,CAACO,aAAa,CAACN,UAAU,EAAEF,QAAQ,CAAC;IACpIS,mBAAmB,EAAE;EACvB,CAAC,EAAEH,KAAK,EAAE;IACRC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AAAA,EAAC;AACJ,eAAeJ,uBAAuB,CAACC,2BAA2B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}