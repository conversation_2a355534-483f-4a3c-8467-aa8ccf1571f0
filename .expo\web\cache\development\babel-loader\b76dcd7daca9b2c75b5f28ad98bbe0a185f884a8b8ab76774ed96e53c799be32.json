{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport NativeAnimatedHelper from \"../NativeAnimatedHelper\";\nvar startNativeAnimationNextId = 1;\nvar Animation = function () {\n  function Animation() {\n    _classCallCheck(this, Animation);\n  }\n  return _createClass(Animation, [{\n    key: \"start\",\n    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {}\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      if (this.__nativeId) {\n        NativeAnimatedHelper.API.stopAnimation(this.__nativeId);\n      }\n    }\n  }, {\n    key: \"__getNativeAnimationConfig\",\n    value: function __getNativeAnimationConfig() {\n      throw new Error('This animation type cannot be offloaded to native');\n    }\n  }, {\n    key: \"__debouncedOnEnd\",\n    value: function __debouncedOnEnd(result) {\n      var onEnd = this.__onEnd;\n      this.__onEnd = null;\n      onEnd && onEnd(result);\n    }\n  }, {\n    key: \"__startNativeAnimation\",\n    value: function __startNativeAnimation(animatedValue) {\n      var startNativeAnimationWaitId = startNativeAnimationNextId + \":startAnimation\";\n      startNativeAnimationNextId += 1;\n      NativeAnimatedHelper.API.setWaitingForIdentifier(startNativeAnimationWaitId);\n      try {\n        var config = this.__getNativeAnimationConfig();\n        animatedValue.__makeNative(config.platformConfig);\n        this.__nativeId = NativeAnimatedHelper.generateNewAnimationId();\n        NativeAnimatedHelper.API.startAnimatingNode(this.__nativeId, animatedValue.__getNativeTag(), config, this.__debouncedOnEnd.bind(this));\n      } catch (e) {\n        throw e;\n      } finally {\n        NativeAnimatedHelper.API.unsetWaitingForIdentifier(startNativeAnimationWaitId);\n      }\n    }\n  }]);\n}();\nexport default Animation;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "NativeAnimatedHelper", "startNativeAnimationNextId", "Animation", "key", "value", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "stop", "__nativeId", "API", "stopAnimation", "__getNativeAnimationConfig", "Error", "__debouncedOnEnd", "result", "__onEnd", "__startNativeAnimation", "startNativeAnimationWaitId", "setWaitingForIdentifier", "config", "__makeNative", "platformConfig", "generateNewAnimationId", "startAnimatingNode", "__getNativeTag", "bind", "e", "unsetWaitingForIdentifier"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/Animated/animations/Animation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport NativeAnimatedHelper from '../NativeAnimatedHelper';\nvar startNativeAnimationNextId = 1;\n\n// Important note: start() and stop() will only be called at most once.\n// Once an animation has been stopped or finished its course, it will\n// not be reused.\nclass Animation {\n  start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {}\n  stop() {\n    if (this.__nativeId) {\n      NativeAnimatedHelper.API.stopAnimation(this.__nativeId);\n    }\n  }\n  __getNativeAnimationConfig() {\n    // Subclasses that have corresponding animation implementation done in native\n    // should override this method\n    throw new Error('This animation type cannot be offloaded to native');\n  }\n  // Helper function for subclasses to make sure onEnd is only called once.\n  __debouncedOnEnd(result) {\n    var onEnd = this.__onEnd;\n    this.__onEnd = null;\n    onEnd && onEnd(result);\n  }\n  __startNativeAnimation(animatedValue) {\n    var startNativeAnimationWaitId = startNativeAnimationNextId + \":startAnimation\";\n    startNativeAnimationNextId += 1;\n    NativeAnimatedHelper.API.setWaitingForIdentifier(startNativeAnimationWaitId);\n    try {\n      var config = this.__getNativeAnimationConfig();\n      animatedValue.__makeNative(config.platformConfig);\n      this.__nativeId = NativeAnimatedHelper.generateNewAnimationId();\n      NativeAnimatedHelper.API.startAnimatingNode(this.__nativeId, animatedValue.__getNativeTag(), config,\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      this.__debouncedOnEnd.bind(this));\n    } catch (e) {\n      throw e;\n    } finally {\n      NativeAnimatedHelper.API.unsetWaitingForIdentifier(startNativeAnimationWaitId);\n    }\n  }\n}\nexport default Animation;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,OAAOC,oBAAoB;AAC3B,IAAIC,0BAA0B,GAAG,CAAC;AAAC,IAK7BC,SAAS;EAAA,SAAAA,UAAA;IAAAJ,eAAA,OAAAI,SAAA;EAAA;EAAA,OAAAH,YAAA,CAAAG,SAAA;IAAAC,GAAA;IAAAC,KAAA,EACb,SAAAC,KAAKA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa,EAAE,CAAC;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACtE,SAAAO,IAAIA,CAAA,EAAG;MACL,IAAI,IAAI,CAACC,UAAU,EAAE;QACnBZ,oBAAoB,CAACa,GAAG,CAACC,aAAa,CAAC,IAAI,CAACF,UAAU,CAAC;MACzD;IACF;EAAC;IAAAT,GAAA;IAAAC,KAAA,EACD,SAAAW,0BAA0BA,CAAA,EAAG;MAG3B,MAAM,IAAIC,KAAK,CAAC,mDAAmD,CAAC;IACtE;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAAa,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAIV,KAAK,GAAG,IAAI,CAACW,OAAO;MACxB,IAAI,CAACA,OAAO,GAAG,IAAI;MACnBX,KAAK,IAAIA,KAAK,CAACU,MAAM,CAAC;IACxB;EAAC;IAAAf,GAAA;IAAAC,KAAA,EACD,SAAAgB,sBAAsBA,CAACV,aAAa,EAAE;MACpC,IAAIW,0BAA0B,GAAGpB,0BAA0B,GAAG,iBAAiB;MAC/EA,0BAA0B,IAAI,CAAC;MAC/BD,oBAAoB,CAACa,GAAG,CAACS,uBAAuB,CAACD,0BAA0B,CAAC;MAC5E,IAAI;QACF,IAAIE,MAAM,GAAG,IAAI,CAACR,0BAA0B,CAAC,CAAC;QAC9CL,aAAa,CAACc,YAAY,CAACD,MAAM,CAACE,cAAc,CAAC;QACjD,IAAI,CAACb,UAAU,GAAGZ,oBAAoB,CAAC0B,sBAAsB,CAAC,CAAC;QAC/D1B,oBAAoB,CAACa,GAAG,CAACc,kBAAkB,CAAC,IAAI,CAACf,UAAU,EAAEF,aAAa,CAACkB,cAAc,CAAC,CAAC,EAAEL,MAAM,EAEnG,IAAI,CAACN,gBAAgB,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC;MACnC,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,MAAMA,CAAC;MACT,CAAC,SAAS;QACR9B,oBAAoB,CAACa,GAAG,CAACkB,yBAAyB,CAACV,0BAA0B,CAAC;MAChF;IACF;EAAC;AAAA;AAEH,eAAenB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}