{"ast": null, "code": "import UIManager from \"../../exports/UIManager\";\nimport useStable from \"../useStable\";\nexport default function usePlatformMethods(_ref) {\n  var pointerEvents = _ref.pointerEvents,\n    style = _ref.style;\n  var ref = useStable(function () {\n    return function (hostNode) {\n      if (hostNode != null) {\n        hostNode.measure = function (callback) {\n          return UIManager.measure(hostNode, callback);\n        };\n        hostNode.measureLayout = function (relativeToNode, success, failure) {\n          return UIManager.measureLayout(hostNode, relativeToNode, failure, success);\n        };\n        hostNode.measureInWindow = function (callback) {\n          return UIManager.measureInWindow(hostNode, callback);\n        };\n      }\n    };\n  });\n  return ref;\n}", "map": {"version": 3, "names": ["UIManager", "useStable", "usePlatformMethods", "_ref", "pointerEvents", "style", "ref", "hostNode", "measure", "callback", "measureLayout", "relativeToNode", "success", "failure", "measureInWindow"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/usePlatformMethods/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport UIManager from '../../exports/UIManager';\nimport useStable from '../useStable';\n\n/**\n * Adds non-standard methods to the hode element. This is temporarily until an\n * API like `ReactNative.measure(hostRef, callback)` is added to React Native.\n */\nexport default function usePlatformMethods(_ref) {\n  var pointerEvents = _ref.pointerEvents,\n    style = _ref.style;\n  // Avoid creating a new ref on every render.\n  var ref = useStable(() => hostNode => {\n    if (hostNode != null) {\n      hostNode.measure = callback => UIManager.measure(hostNode, callback);\n      hostNode.measureLayout = (relativeToNode, success, failure) => UIManager.measureLayout(hostNode, relativeToNode, failure, success);\n      hostNode.measureInWindow = callback => UIManager.measureInWindow(hostNode, callback);\n    }\n  });\n  return ref;\n}"], "mappings": "AASA,OAAOA,SAAS;AAChB,OAAOC,SAAS;AAMhB,eAAe,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EAC/C,IAAIC,aAAa,GAAGD,IAAI,CAACC,aAAa;IACpCC,KAAK,GAAGF,IAAI,CAACE,KAAK;EAEpB,IAAIC,GAAG,GAAGL,SAAS,CAAC;IAAA,OAAM,UAAAM,QAAQ,EAAI;MACpC,IAAIA,QAAQ,IAAI,IAAI,EAAE;QACpBA,QAAQ,CAACC,OAAO,GAAG,UAAAC,QAAQ;UAAA,OAAIT,SAAS,CAACQ,OAAO,CAACD,QAAQ,EAAEE,QAAQ,CAAC;QAAA;QACpEF,QAAQ,CAACG,aAAa,GAAG,UAACC,cAAc,EAAEC,OAAO,EAAEC,OAAO;UAAA,OAAKb,SAAS,CAACU,aAAa,CAACH,QAAQ,EAAEI,cAAc,EAAEE,OAAO,EAAED,OAAO,CAAC;QAAA;QAClIL,QAAQ,CAACO,eAAe,GAAG,UAAAL,QAAQ;UAAA,OAAIT,SAAS,CAACc,eAAe,CAACP,QAAQ,EAAEE,QAAQ,CAAC;QAAA;MACtF;IACF,CAAC;EAAA,EAAC;EACF,OAAOH,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}