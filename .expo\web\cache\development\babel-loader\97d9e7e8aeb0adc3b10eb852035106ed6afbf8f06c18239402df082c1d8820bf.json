{"ast": null, "code": "import NativeModulesProxy from \"../NativeModulesProxy\";\nexport default NativeModulesProxy.ExpoModulesCoreErrorManager;", "map": {"version": 3, "names": ["NativeModulesProxy", "ExpoModulesCoreErrorManager"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/expo-modules-core/src/sweet/NativeErrorManager.ts"], "sourcesContent": ["import NativeModulesProxy from '../NativeModulesProxy';\nexport default NativeModulesProxy.ExpoModulesCoreErrorManager;\n"], "mappings": "AAAA,OAAOA,kBAAkB;AACzB,eAAeA,kBAAkB,CAACC,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}