{"ast": null, "code": "import RCTDeviceEventEmitter from \"../../vendor/react-native/EventEmitter/RCTDeviceEventEmitter\";\nexport default RCTDeviceEventEmitter;", "map": {"version": 3, "names": ["RCTDeviceEventEmitter"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/exports/DeviceEventEmitter/index.js"], "sourcesContent": ["import RCTDeviceEventEmitter from '../../vendor/react-native/EventEmitter/RCTDeviceEventEmitter';\nexport default RCTDeviceEventEmitter;"], "mappings": "AAAA,OAAOA,qBAAqB;AAC5B,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}