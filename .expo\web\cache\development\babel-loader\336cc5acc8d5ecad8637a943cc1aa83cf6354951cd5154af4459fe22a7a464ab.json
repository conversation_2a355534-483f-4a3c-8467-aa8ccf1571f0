{"ast": null, "code": "import * as React from 'react';\nimport mergeRefs from \"../mergeRefs\";\nexport default function useMergeRefs() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return React.useMemo(function () {\n    return mergeRefs.apply(void 0, args);\n  }, [].concat(args));\n}", "map": {"version": 3, "names": ["React", "mergeRefs", "useMergeRefs", "_len", "arguments", "length", "args", "Array", "_key", "useMemo", "apply", "concat"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/useMergeRefs/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport mergeRefs from '../mergeRefs';\nexport default function useMergeRefs() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return React.useMemo(() => mergeRefs(...args),\n  // eslint-disable-next-line\n  [...args]);\n}"], "mappings": "AASA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS;AAChB,eAAe,SAASC,YAAYA,CAAA,EAAG;EACrC,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,OAAOR,KAAK,CAACS,OAAO,CAAC;IAAA,OAAMR,SAAS,CAAAS,KAAA,SAAIJ,IAAI,CAAC;EAAA,MAAAK,MAAA,CAEzCL,IAAI,CAAC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}