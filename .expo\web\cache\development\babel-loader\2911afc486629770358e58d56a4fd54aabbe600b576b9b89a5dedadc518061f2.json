{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport { isStartish, isMoveish, isEndish } from \"./ResponderEventTypes\";\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nvar MAX_TOUCH_BANK = 20;\nfunction timestampForTouch(touch) {\n  return touch.timeStamp || touch.timestamp;\n}\nfunction createTouchRecord(touch) {\n  return {\n    touchActive: true,\n    startPageX: touch.pageX,\n    startPageY: touch.pageY,\n    startTimeStamp: timestampForTouch(touch),\n    currentPageX: touch.pageX,\n    currentPageY: touch.pageY,\n    currentTimeStamp: timestampForTouch(touch),\n    previousPageX: touch.pageX,\n    previousPageY: touch.pageY,\n    previousTimeStamp: timestampForTouch(touch)\n  };\n}\nfunction resetTouchRecord(touchRecord, touch) {\n  touchRecord.touchActive = true;\n  touchRecord.startPageX = touch.pageX;\n  touchRecord.startPageY = touch.pageY;\n  touchRecord.startTimeStamp = timestampForTouch(touch);\n  touchRecord.currentPageX = touch.pageX;\n  touchRecord.currentPageY = touch.pageY;\n  touchRecord.currentTimeStamp = timestampForTouch(touch);\n  touchRecord.previousPageX = touch.pageX;\n  touchRecord.previousPageY = touch.pageY;\n  touchRecord.previousTimeStamp = timestampForTouch(touch);\n}\nfunction getTouchIdentifier(_ref) {\n  var identifier = _ref.identifier;\n  if (identifier == null) {\n    console.error('Touch object is missing identifier.');\n  }\n  if (__DEV__) {\n    if (identifier > MAX_TOUCH_BANK) {\n      console.error('Touch identifier %s is greater than maximum supported %s which causes ' + 'performance issues backfilling array locations for all of the indices.', identifier, MAX_TOUCH_BANK);\n    }\n  }\n  return identifier;\n}\nfunction recordTouchStart(touch, touchHistory) {\n  var identifier = getTouchIdentifier(touch);\n  var touchRecord = touchHistory.touchBank[identifier];\n  if (touchRecord) {\n    resetTouchRecord(touchRecord, touch);\n  } else {\n    touchHistory.touchBank[identifier] = createTouchRecord(touch);\n  }\n  touchHistory.mostRecentTimeStamp = timestampForTouch(touch);\n}\nfunction recordTouchMove(touch, touchHistory) {\n  var touchRecord = touchHistory.touchBank[getTouchIdentifier(touch)];\n  if (touchRecord) {\n    touchRecord.touchActive = true;\n    touchRecord.previousPageX = touchRecord.currentPageX;\n    touchRecord.previousPageY = touchRecord.currentPageY;\n    touchRecord.previousTimeStamp = touchRecord.currentTimeStamp;\n    touchRecord.currentPageX = touch.pageX;\n    touchRecord.currentPageY = touch.pageY;\n    touchRecord.currentTimeStamp = timestampForTouch(touch);\n    touchHistory.mostRecentTimeStamp = timestampForTouch(touch);\n  } else {\n    console.warn('Cannot record touch move without a touch start.\\n', \"Touch Move: \" + printTouch(touch) + \"\\n\", \"Touch Bank: \" + printTouchBank(touchHistory));\n  }\n}\nfunction recordTouchEnd(touch, touchHistory) {\n  var touchRecord = touchHistory.touchBank[getTouchIdentifier(touch)];\n  if (touchRecord) {\n    touchRecord.touchActive = false;\n    touchRecord.previousPageX = touchRecord.currentPageX;\n    touchRecord.previousPageY = touchRecord.currentPageY;\n    touchRecord.previousTimeStamp = touchRecord.currentTimeStamp;\n    touchRecord.currentPageX = touch.pageX;\n    touchRecord.currentPageY = touch.pageY;\n    touchRecord.currentTimeStamp = timestampForTouch(touch);\n    touchHistory.mostRecentTimeStamp = timestampForTouch(touch);\n  } else {\n    console.warn('Cannot record touch end without a touch start.\\n', \"Touch End: \" + printTouch(touch) + \"\\n\", \"Touch Bank: \" + printTouchBank(touchHistory));\n  }\n}\nfunction printTouch(touch) {\n  return JSON.stringify({\n    identifier: touch.identifier,\n    pageX: touch.pageX,\n    pageY: touch.pageY,\n    timestamp: timestampForTouch(touch)\n  });\n}\nfunction printTouchBank(touchHistory) {\n  var touchBank = touchHistory.touchBank;\n  var printed = JSON.stringify(touchBank.slice(0, MAX_TOUCH_BANK));\n  if (touchBank.length > MAX_TOUCH_BANK) {\n    printed += ' (original size: ' + touchBank.length + ')';\n  }\n  return printed;\n}\nexport var ResponderTouchHistoryStore = function () {\n  function ResponderTouchHistoryStore() {\n    _classCallCheck(this, ResponderTouchHistoryStore);\n    this._touchHistory = {\n      touchBank: [],\n      numberActiveTouches: 0,\n      indexOfSingleActiveTouch: -1,\n      mostRecentTimeStamp: 0\n    };\n  }\n  return _createClass(ResponderTouchHistoryStore, [{\n    key: \"recordTouchTrack\",\n    value: function recordTouchTrack(topLevelType, nativeEvent) {\n      var touchHistory = this._touchHistory;\n      if (isMoveish(topLevelType)) {\n        nativeEvent.changedTouches.forEach(function (touch) {\n          return recordTouchMove(touch, touchHistory);\n        });\n      } else if (isStartish(topLevelType)) {\n        nativeEvent.changedTouches.forEach(function (touch) {\n          return recordTouchStart(touch, touchHistory);\n        });\n        touchHistory.numberActiveTouches = nativeEvent.touches.length;\n        if (touchHistory.numberActiveTouches === 1) {\n          touchHistory.indexOfSingleActiveTouch = nativeEvent.touches[0].identifier;\n        }\n      } else if (isEndish(topLevelType)) {\n        nativeEvent.changedTouches.forEach(function (touch) {\n          return recordTouchEnd(touch, touchHistory);\n        });\n        touchHistory.numberActiveTouches = nativeEvent.touches.length;\n        if (touchHistory.numberActiveTouches === 1) {\n          var touchBank = touchHistory.touchBank;\n          for (var i = 0; i < touchBank.length; i++) {\n            var touchTrackToCheck = touchBank[i];\n            if (touchTrackToCheck != null && touchTrackToCheck.touchActive) {\n              touchHistory.indexOfSingleActiveTouch = i;\n              break;\n            }\n          }\n          if (__DEV__) {\n            var activeRecord = touchBank[touchHistory.indexOfSingleActiveTouch];\n            if (!(activeRecord != null && activeRecord.touchActive)) {\n              console.error('Cannot find single active touch.');\n            }\n          }\n        }\n      }\n    }\n  }, {\n    key: \"touchHistory\",\n    get: function get() {\n      return this._touchHistory;\n    }\n  }]);\n}();", "map": {"version": 3, "names": ["isStartish", "isMoveish", "<PERSON><PERSON><PERSON><PERSON>", "__DEV__", "process", "env", "NODE_ENV", "MAX_TOUCH_BANK", "timestampForTouch", "touch", "timeStamp", "timestamp", "createTouchRecord", "touchActive", "startPageX", "pageX", "startPageY", "pageY", "startTimeStamp", "currentPageX", "currentPageY", "currentTimeStamp", "previousPageX", "previousPageY", "previousTimeStamp", "resetTouchRecord", "touchRecord", "getTouchIdentifier", "_ref", "identifier", "console", "error", "recordTouchStart", "touchHistory", "touchBank", "mostRecentTimeStamp", "recordTouchMove", "warn", "printTouch", "printTouchBank", "recordTouchEnd", "JSON", "stringify", "printed", "slice", "length", "ResponderTouchHistoryStore", "_classCallCheck", "_touchHistory", "numberActiveTouches", "indexOfSingleActiveTouch", "_createClass", "key", "value", "recordTouchTrack", "topLevelType", "nativeEvent", "changedTouches", "for<PERSON>ach", "touches", "i", "touchTrackToCheck", "activeRecord", "get"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/useResponderEvents/ResponderTouchHistoryStore.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport { isStartish, isMoveish, isEndish } from './ResponderEventTypes';\n/**\n * Tracks the position and time of each active touch by `touch.identifier`. We\n * should typically only see IDs in the range of 1-20 because IDs get recycled\n * when touches end and start again.\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nvar MAX_TOUCH_BANK = 20;\nfunction timestampForTouch(touch) {\n  // The legacy internal implementation provides \"timeStamp\", which has been\n  // renamed to \"timestamp\".\n  return touch.timeStamp || touch.timestamp;\n}\n\n/**\n * TODO: Instead of making gestures recompute filtered velocity, we could\n * include a built in velocity computation that can be reused globally.\n */\nfunction createTouchRecord(touch) {\n  return {\n    touchActive: true,\n    startPageX: touch.pageX,\n    startPageY: touch.pageY,\n    startTimeStamp: timestampForTouch(touch),\n    currentPageX: touch.pageX,\n    currentPageY: touch.pageY,\n    currentTimeStamp: timestampForTouch(touch),\n    previousPageX: touch.pageX,\n    previousPageY: touch.pageY,\n    previousTimeStamp: timestampForTouch(touch)\n  };\n}\nfunction resetTouchRecord(touchRecord, touch) {\n  touchRecord.touchActive = true;\n  touchRecord.startPageX = touch.pageX;\n  touchRecord.startPageY = touch.pageY;\n  touchRecord.startTimeStamp = timestampForTouch(touch);\n  touchRecord.currentPageX = touch.pageX;\n  touchRecord.currentPageY = touch.pageY;\n  touchRecord.currentTimeStamp = timestampForTouch(touch);\n  touchRecord.previousPageX = touch.pageX;\n  touchRecord.previousPageY = touch.pageY;\n  touchRecord.previousTimeStamp = timestampForTouch(touch);\n}\nfunction getTouchIdentifier(_ref) {\n  var identifier = _ref.identifier;\n  if (identifier == null) {\n    console.error('Touch object is missing identifier.');\n  }\n  if (__DEV__) {\n    if (identifier > MAX_TOUCH_BANK) {\n      console.error('Touch identifier %s is greater than maximum supported %s which causes ' + 'performance issues backfilling array locations for all of the indices.', identifier, MAX_TOUCH_BANK);\n    }\n  }\n  return identifier;\n}\nfunction recordTouchStart(touch, touchHistory) {\n  var identifier = getTouchIdentifier(touch);\n  var touchRecord = touchHistory.touchBank[identifier];\n  if (touchRecord) {\n    resetTouchRecord(touchRecord, touch);\n  } else {\n    touchHistory.touchBank[identifier] = createTouchRecord(touch);\n  }\n  touchHistory.mostRecentTimeStamp = timestampForTouch(touch);\n}\nfunction recordTouchMove(touch, touchHistory) {\n  var touchRecord = touchHistory.touchBank[getTouchIdentifier(touch)];\n  if (touchRecord) {\n    touchRecord.touchActive = true;\n    touchRecord.previousPageX = touchRecord.currentPageX;\n    touchRecord.previousPageY = touchRecord.currentPageY;\n    touchRecord.previousTimeStamp = touchRecord.currentTimeStamp;\n    touchRecord.currentPageX = touch.pageX;\n    touchRecord.currentPageY = touch.pageY;\n    touchRecord.currentTimeStamp = timestampForTouch(touch);\n    touchHistory.mostRecentTimeStamp = timestampForTouch(touch);\n  } else {\n    console.warn('Cannot record touch move without a touch start.\\n', \"Touch Move: \" + printTouch(touch) + \"\\n\", \"Touch Bank: \" + printTouchBank(touchHistory));\n  }\n}\nfunction recordTouchEnd(touch, touchHistory) {\n  var touchRecord = touchHistory.touchBank[getTouchIdentifier(touch)];\n  if (touchRecord) {\n    touchRecord.touchActive = false;\n    touchRecord.previousPageX = touchRecord.currentPageX;\n    touchRecord.previousPageY = touchRecord.currentPageY;\n    touchRecord.previousTimeStamp = touchRecord.currentTimeStamp;\n    touchRecord.currentPageX = touch.pageX;\n    touchRecord.currentPageY = touch.pageY;\n    touchRecord.currentTimeStamp = timestampForTouch(touch);\n    touchHistory.mostRecentTimeStamp = timestampForTouch(touch);\n  } else {\n    console.warn('Cannot record touch end without a touch start.\\n', \"Touch End: \" + printTouch(touch) + \"\\n\", \"Touch Bank: \" + printTouchBank(touchHistory));\n  }\n}\nfunction printTouch(touch) {\n  return JSON.stringify({\n    identifier: touch.identifier,\n    pageX: touch.pageX,\n    pageY: touch.pageY,\n    timestamp: timestampForTouch(touch)\n  });\n}\nfunction printTouchBank(touchHistory) {\n  var touchBank = touchHistory.touchBank;\n  var printed = JSON.stringify(touchBank.slice(0, MAX_TOUCH_BANK));\n  if (touchBank.length > MAX_TOUCH_BANK) {\n    printed += ' (original size: ' + touchBank.length + ')';\n  }\n  return printed;\n}\nexport class ResponderTouchHistoryStore {\n  constructor() {\n    this._touchHistory = {\n      touchBank: [],\n      //Array<TouchRecord>\n      numberActiveTouches: 0,\n      // If there is only one active touch, we remember its location. This prevents\n      // us having to loop through all of the touches all the time in the most\n      // common case.\n      indexOfSingleActiveTouch: -1,\n      mostRecentTimeStamp: 0\n    };\n  }\n  recordTouchTrack(topLevelType, nativeEvent) {\n    var touchHistory = this._touchHistory;\n    if (isMoveish(topLevelType)) {\n      nativeEvent.changedTouches.forEach(touch => recordTouchMove(touch, touchHistory));\n    } else if (isStartish(topLevelType)) {\n      nativeEvent.changedTouches.forEach(touch => recordTouchStart(touch, touchHistory));\n      touchHistory.numberActiveTouches = nativeEvent.touches.length;\n      if (touchHistory.numberActiveTouches === 1) {\n        touchHistory.indexOfSingleActiveTouch = nativeEvent.touches[0].identifier;\n      }\n    } else if (isEndish(topLevelType)) {\n      nativeEvent.changedTouches.forEach(touch => recordTouchEnd(touch, touchHistory));\n      touchHistory.numberActiveTouches = nativeEvent.touches.length;\n      if (touchHistory.numberActiveTouches === 1) {\n        var touchBank = touchHistory.touchBank;\n        for (var i = 0; i < touchBank.length; i++) {\n          var touchTrackToCheck = touchBank[i];\n          if (touchTrackToCheck != null && touchTrackToCheck.touchActive) {\n            touchHistory.indexOfSingleActiveTouch = i;\n            break;\n          }\n        }\n        if (__DEV__) {\n          var activeRecord = touchBank[touchHistory.indexOfSingleActiveTouch];\n          if (!(activeRecord != null && activeRecord.touchActive)) {\n            console.error('Cannot find single active touch.');\n          }\n        }\n      }\n    }\n  }\n  get touchHistory() {\n    return this._touchHistory;\n  }\n}"], "mappings": ";;AASA,SAASA,UAAU,EAAEC,SAAS,EAAEC,QAAQ;AAOxC,IAAIC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACnD,IAAIC,cAAc,GAAG,EAAE;AACvB,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAGhC,OAAOA,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,SAAS;AAC3C;AAMA,SAASC,iBAAiBA,CAACH,KAAK,EAAE;EAChC,OAAO;IACLI,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAEL,KAAK,CAACM,KAAK;IACvBC,UAAU,EAAEP,KAAK,CAACQ,KAAK;IACvBC,cAAc,EAAEV,iBAAiB,CAACC,KAAK,CAAC;IACxCU,YAAY,EAAEV,KAAK,CAACM,KAAK;IACzBK,YAAY,EAAEX,KAAK,CAACQ,KAAK;IACzBI,gBAAgB,EAAEb,iBAAiB,CAACC,KAAK,CAAC;IAC1Ca,aAAa,EAAEb,KAAK,CAACM,KAAK;IAC1BQ,aAAa,EAAEd,KAAK,CAACQ,KAAK;IAC1BO,iBAAiB,EAAEhB,iBAAiB,CAACC,KAAK;EAC5C,CAAC;AACH;AACA,SAASgB,gBAAgBA,CAACC,WAAW,EAAEjB,KAAK,EAAE;EAC5CiB,WAAW,CAACb,WAAW,GAAG,IAAI;EAC9Ba,WAAW,CAACZ,UAAU,GAAGL,KAAK,CAACM,KAAK;EACpCW,WAAW,CAACV,UAAU,GAAGP,KAAK,CAACQ,KAAK;EACpCS,WAAW,CAACR,cAAc,GAAGV,iBAAiB,CAACC,KAAK,CAAC;EACrDiB,WAAW,CAACP,YAAY,GAAGV,KAAK,CAACM,KAAK;EACtCW,WAAW,CAACN,YAAY,GAAGX,KAAK,CAACQ,KAAK;EACtCS,WAAW,CAACL,gBAAgB,GAAGb,iBAAiB,CAACC,KAAK,CAAC;EACvDiB,WAAW,CAACJ,aAAa,GAAGb,KAAK,CAACM,KAAK;EACvCW,WAAW,CAACH,aAAa,GAAGd,KAAK,CAACQ,KAAK;EACvCS,WAAW,CAACF,iBAAiB,GAAGhB,iBAAiB,CAACC,KAAK,CAAC;AAC1D;AACA,SAASkB,kBAAkBA,CAACC,IAAI,EAAE;EAChC,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;EAChC,IAAIA,UAAU,IAAI,IAAI,EAAE;IACtBC,OAAO,CAACC,KAAK,CAAC,qCAAqC,CAAC;EACtD;EACA,IAAI5B,OAAO,EAAE;IACX,IAAI0B,UAAU,GAAGtB,cAAc,EAAE;MAC/BuB,OAAO,CAACC,KAAK,CAAC,wEAAwE,GAAG,wEAAwE,EAAEF,UAAU,EAAEtB,cAAc,CAAC;IAChM;EACF;EACA,OAAOsB,UAAU;AACnB;AACA,SAASG,gBAAgBA,CAACvB,KAAK,EAAEwB,YAAY,EAAE;EAC7C,IAAIJ,UAAU,GAAGF,kBAAkB,CAAClB,KAAK,CAAC;EAC1C,IAAIiB,WAAW,GAAGO,YAAY,CAACC,SAAS,CAACL,UAAU,CAAC;EACpD,IAAIH,WAAW,EAAE;IACfD,gBAAgB,CAACC,WAAW,EAAEjB,KAAK,CAAC;EACtC,CAAC,MAAM;IACLwB,YAAY,CAACC,SAAS,CAACL,UAAU,CAAC,GAAGjB,iBAAiB,CAACH,KAAK,CAAC;EAC/D;EACAwB,YAAY,CAACE,mBAAmB,GAAG3B,iBAAiB,CAACC,KAAK,CAAC;AAC7D;AACA,SAAS2B,eAAeA,CAAC3B,KAAK,EAAEwB,YAAY,EAAE;EAC5C,IAAIP,WAAW,GAAGO,YAAY,CAACC,SAAS,CAACP,kBAAkB,CAAClB,KAAK,CAAC,CAAC;EACnE,IAAIiB,WAAW,EAAE;IACfA,WAAW,CAACb,WAAW,GAAG,IAAI;IAC9Ba,WAAW,CAACJ,aAAa,GAAGI,WAAW,CAACP,YAAY;IACpDO,WAAW,CAACH,aAAa,GAAGG,WAAW,CAACN,YAAY;IACpDM,WAAW,CAACF,iBAAiB,GAAGE,WAAW,CAACL,gBAAgB;IAC5DK,WAAW,CAACP,YAAY,GAAGV,KAAK,CAACM,KAAK;IACtCW,WAAW,CAACN,YAAY,GAAGX,KAAK,CAACQ,KAAK;IACtCS,WAAW,CAACL,gBAAgB,GAAGb,iBAAiB,CAACC,KAAK,CAAC;IACvDwB,YAAY,CAACE,mBAAmB,GAAG3B,iBAAiB,CAACC,KAAK,CAAC;EAC7D,CAAC,MAAM;IACLqB,OAAO,CAACO,IAAI,CAAC,mDAAmD,EAAE,cAAc,GAAGC,UAAU,CAAC7B,KAAK,CAAC,GAAG,IAAI,EAAE,cAAc,GAAG8B,cAAc,CAACN,YAAY,CAAC,CAAC;EAC7J;AACF;AACA,SAASO,cAAcA,CAAC/B,KAAK,EAAEwB,YAAY,EAAE;EAC3C,IAAIP,WAAW,GAAGO,YAAY,CAACC,SAAS,CAACP,kBAAkB,CAAClB,KAAK,CAAC,CAAC;EACnE,IAAIiB,WAAW,EAAE;IACfA,WAAW,CAACb,WAAW,GAAG,KAAK;IAC/Ba,WAAW,CAACJ,aAAa,GAAGI,WAAW,CAACP,YAAY;IACpDO,WAAW,CAACH,aAAa,GAAGG,WAAW,CAACN,YAAY;IACpDM,WAAW,CAACF,iBAAiB,GAAGE,WAAW,CAACL,gBAAgB;IAC5DK,WAAW,CAACP,YAAY,GAAGV,KAAK,CAACM,KAAK;IACtCW,WAAW,CAACN,YAAY,GAAGX,KAAK,CAACQ,KAAK;IACtCS,WAAW,CAACL,gBAAgB,GAAGb,iBAAiB,CAACC,KAAK,CAAC;IACvDwB,YAAY,CAACE,mBAAmB,GAAG3B,iBAAiB,CAACC,KAAK,CAAC;EAC7D,CAAC,MAAM;IACLqB,OAAO,CAACO,IAAI,CAAC,kDAAkD,EAAE,aAAa,GAAGC,UAAU,CAAC7B,KAAK,CAAC,GAAG,IAAI,EAAE,cAAc,GAAG8B,cAAc,CAACN,YAAY,CAAC,CAAC;EAC3J;AACF;AACA,SAASK,UAAUA,CAAC7B,KAAK,EAAE;EACzB,OAAOgC,IAAI,CAACC,SAAS,CAAC;IACpBb,UAAU,EAAEpB,KAAK,CAACoB,UAAU;IAC5Bd,KAAK,EAAEN,KAAK,CAACM,KAAK;IAClBE,KAAK,EAAER,KAAK,CAACQ,KAAK;IAClBN,SAAS,EAAEH,iBAAiB,CAACC,KAAK;EACpC,CAAC,CAAC;AACJ;AACA,SAAS8B,cAAcA,CAACN,YAAY,EAAE;EACpC,IAAIC,SAAS,GAAGD,YAAY,CAACC,SAAS;EACtC,IAAIS,OAAO,GAAGF,IAAI,CAACC,SAAS,CAACR,SAAS,CAACU,KAAK,CAAC,CAAC,EAAErC,cAAc,CAAC,CAAC;EAChE,IAAI2B,SAAS,CAACW,MAAM,GAAGtC,cAAc,EAAE;IACrCoC,OAAO,IAAI,mBAAmB,GAAGT,SAAS,CAACW,MAAM,GAAG,GAAG;EACzD;EACA,OAAOF,OAAO;AAChB;AACA,WAAaG,0BAA0B;EACrC,SAAAA,2BAAA,EAAc;IAAAC,eAAA,OAAAD,0BAAA;IACZ,IAAI,CAACE,aAAa,GAAG;MACnBd,SAAS,EAAE,EAAE;MAEbe,mBAAmB,EAAE,CAAC;MAItBC,wBAAwB,EAAE,CAAC,CAAC;MAC5Bf,mBAAmB,EAAE;IACvB,CAAC;EACH;EAAC,OAAAgB,YAAA,CAAAL,0BAAA;IAAAM,GAAA;IAAAC,KAAA,EACD,SAAAC,gBAAgBA,CAACC,YAAY,EAAEC,WAAW,EAAE;MAC1C,IAAIvB,YAAY,GAAG,IAAI,CAACe,aAAa;MACrC,IAAI/C,SAAS,CAACsD,YAAY,CAAC,EAAE;QAC3BC,WAAW,CAACC,cAAc,CAACC,OAAO,CAAC,UAAAjD,KAAK;UAAA,OAAI2B,eAAe,CAAC3B,KAAK,EAAEwB,YAAY,CAAC;QAAA,EAAC;MACnF,CAAC,MAAM,IAAIjC,UAAU,CAACuD,YAAY,CAAC,EAAE;QACnCC,WAAW,CAACC,cAAc,CAACC,OAAO,CAAC,UAAAjD,KAAK;UAAA,OAAIuB,gBAAgB,CAACvB,KAAK,EAAEwB,YAAY,CAAC;QAAA,EAAC;QAClFA,YAAY,CAACgB,mBAAmB,GAAGO,WAAW,CAACG,OAAO,CAACd,MAAM;QAC7D,IAAIZ,YAAY,CAACgB,mBAAmB,KAAK,CAAC,EAAE;UAC1ChB,YAAY,CAACiB,wBAAwB,GAAGM,WAAW,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC9B,UAAU;QAC3E;MACF,CAAC,MAAM,IAAI3B,QAAQ,CAACqD,YAAY,CAAC,EAAE;QACjCC,WAAW,CAACC,cAAc,CAACC,OAAO,CAAC,UAAAjD,KAAK;UAAA,OAAI+B,cAAc,CAAC/B,KAAK,EAAEwB,YAAY,CAAC;QAAA,EAAC;QAChFA,YAAY,CAACgB,mBAAmB,GAAGO,WAAW,CAACG,OAAO,CAACd,MAAM;QAC7D,IAAIZ,YAAY,CAACgB,mBAAmB,KAAK,CAAC,EAAE;UAC1C,IAAIf,SAAS,GAAGD,YAAY,CAACC,SAAS;UACtC,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,SAAS,CAACW,MAAM,EAAEe,CAAC,EAAE,EAAE;YACzC,IAAIC,iBAAiB,GAAG3B,SAAS,CAAC0B,CAAC,CAAC;YACpC,IAAIC,iBAAiB,IAAI,IAAI,IAAIA,iBAAiB,CAAChD,WAAW,EAAE;cAC9DoB,YAAY,CAACiB,wBAAwB,GAAGU,CAAC;cACzC;YACF;UACF;UACA,IAAIzD,OAAO,EAAE;YACX,IAAI2D,YAAY,GAAG5B,SAAS,CAACD,YAAY,CAACiB,wBAAwB,CAAC;YACnE,IAAI,EAAEY,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACjD,WAAW,CAAC,EAAE;cACvDiB,OAAO,CAACC,KAAK,CAAC,kCAAkC,CAAC;YACnD;UACF;QACF;MACF;IACF;EAAC;IAAAqB,GAAA;IAAAW,GAAA,EACD,SAAAA,IAAA,EAAmB;MACjB,OAAO,IAAI,CAACf,aAAa;IAC3B;EAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}