{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport ActivityIndicator from \"react-native-web/dist/exports/ActivityIndicator\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar SplashScreen = function SplashScreen() {\n  return _jsx(View, {\n    style: styles.container,\n    children: _jsxs(View, {\n      style: styles.content,\n      children: [_jsx(View, {\n        style: styles.logoContainer,\n        children: _jsx(View, {\n          style: styles.logoCircle,\n          children: _jsxs(View, {\n            style: styles.globeIcon,\n            children: [_jsx(View, {\n              style: styles.globeGrid\n            }), _jsx(View, {\n              style: styles.dot1\n            }), _jsx(View, {\n              style: styles.dot2\n            }), _jsx(View, {\n              style: styles.dot3\n            }), _jsx(View, {\n              style: styles.dot4\n            })]\n          })\n        })\n      }), _jsxs(View, {\n        style: styles.titleContainer,\n        children: [_jsxs(Text, {\n          style: styles.title,\n          children: [\"Link\", _jsx(Text, {\n            style: styles.titleBlue,\n            children: \"\\u25CF\"\n          }), \"LOGISTICS\"]\n        }), _jsx(Text, {\n          style: styles.subtitle,\n          children: \"Connecting global logistics solutions\"\n        })]\n      }), _jsxs(View, {\n        style: styles.loadingContainer,\n        children: [_jsx(ActivityIndicator, {\n          size: \"small\",\n          color: \"#4A90E2\"\n        }), _jsx(Text, {\n          style: styles.loadingText,\n          children: \"Loading your logistics world...\"\n        })]\n      })]\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: \"#E8F4FD\",\n    justifyContent: \"center\",\n    alignItems: \"center\"\n  },\n  content: {\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  },\n  logoContainer: {\n    marginBottom: 40\n  },\n  logoCircle: {\n    width: 120,\n    height: 120,\n    borderRadius: 60,\n    backgroundColor: \"#4A90E2\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    shadowColor: \"#000\",\n    shadowOffset: {\n      width: 0,\n      height: 4\n    },\n    shadowOpacity: 0.3,\n    shadowRadius: 4.65,\n    elevation: 8\n  },\n  globeIcon: {\n    width: 60,\n    height: 60,\n    position: \"relative\"\n  },\n  globeGrid: {\n    width: 60,\n    height: 60,\n    borderRadius: 30,\n    borderWidth: 2,\n    borderColor: \"white\",\n    position: \"absolute\"\n  },\n  dot1: {\n    width: 6,\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: \"white\",\n    position: \"absolute\",\n    top: 10,\n    left: 15\n  },\n  dot2: {\n    width: 6,\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: \"white\",\n    position: \"absolute\",\n    top: 20,\n    right: 10\n  },\n  dot3: {\n    width: 6,\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: \"white\",\n    position: \"absolute\",\n    bottom: 15,\n    left: 20\n  },\n  dot4: {\n    width: 6,\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: \"white\",\n    position: \"absolute\",\n    bottom: 10,\n    right: 15\n  },\n  titleContainer: {\n    alignItems: \"center\",\n    marginBottom: 60\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: \"bold\",\n    color: \"#333\",\n    marginBottom: 8\n  },\n  titleBlue: {\n    color: \"#4A90E2\"\n  },\n  subtitle: {\n    fontSize: 16,\n    color: \"#666\",\n    textAlign: \"center\"\n  },\n  loadingContainer: {\n    flexDirection: \"row\",\n    alignItems: \"center\"\n  },\n  loadingText: {\n    marginLeft: 10,\n    fontSize: 14,\n    color: \"#666\"\n  }\n});\nexport default SplashScreen;", "map": {"version": 3, "names": ["SplashScreen", "_jsx", "View", "style", "styles", "container", "children", "_jsxs", "content", "logoContainer", "logoCircle", "globeIcon", "globeGrid", "dot1", "dot2", "dot3", "dot4", "<PERSON><PERSON><PERSON><PERSON>", "Text", "title", "titleBlue", "subtitle", "loadingContainer", "ActivityIndicator", "size", "color", "loadingText", "StyleSheet", "create", "flex", "backgroundColor", "justifyContent", "alignItems", "marginBottom", "width", "height", "borderRadius", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "elevation", "position", "borderWidth", "borderColor", "top", "left", "right", "bottom", "fontSize", "fontWeight", "textAlign", "flexDirection", "marginLeft"], "sources": ["/Users/<USER>/Downloads/logistics-app/src/screens/SplashScreen.tsx"], "sourcesContent": ["import { View, Text, StyleSheet, ActivityIndicator } from \"react-native\"\n\nconst SplashScreen = () => {\n  return (\n    <View style={styles.container}>\n      <View style={styles.content}>\n        <View style={styles.logoContainer}>\n          <View style={styles.logoCircle}>\n            <View style={styles.globeIcon}>\n              <View style={styles.globeGrid} />\n              <View style={styles.dot1} />\n              <View style={styles.dot2} />\n              <View style={styles.dot3} />\n              <View style={styles.dot4} />\n            </View>\n          </View>\n        </View>\n\n        <View style={styles.titleContainer}>\n          <Text style={styles.title}>\n            Link<Text style={styles.titleBlue}>●</Text>LOGISTICS\n          </Text>\n          <Text style={styles.subtitle}>Connecting global logistics solutions</Text>\n        </View>\n\n        <View style={styles.loadingContainer}>\n          <ActivityIndicator size=\"small\" color=\"#4A90E2\" />\n          <Text style={styles.loadingText}>Loading your logistics world...</Text>\n        </View>\n      </View>\n    </View>\n  )\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: \"#E8F4FD\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n  },\n  content: {\n    alignItems: \"center\",\n    justifyContent: \"center\",\n  },\n  logoContainer: {\n    marginBottom: 40,\n  },\n  logoCircle: {\n    width: 120,\n    height: 120,\n    borderRadius: 60,\n    backgroundColor: \"#4A90E2\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    shadowColor: \"#000\",\n    shadowOffset: {\n      width: 0,\n      height: 4,\n    },\n    shadowOpacity: 0.3,\n    shadowRadius: 4.65,\n    elevation: 8,\n  },\n  globeIcon: {\n    width: 60,\n    height: 60,\n    position: \"relative\",\n  },\n  globeGrid: {\n    width: 60,\n    height: 60,\n    borderRadius: 30,\n    borderWidth: 2,\n    borderColor: \"white\",\n    position: \"absolute\",\n  },\n  dot1: {\n    width: 6,\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: \"white\",\n    position: \"absolute\",\n    top: 10,\n    left: 15,\n  },\n  dot2: {\n    width: 6,\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: \"white\",\n    position: \"absolute\",\n    top: 20,\n    right: 10,\n  },\n  dot3: {\n    width: 6,\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: \"white\",\n    position: \"absolute\",\n    bottom: 15,\n    left: 20,\n  },\n  dot4: {\n    width: 6,\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: \"white\",\n    position: \"absolute\",\n    bottom: 10,\n    right: 15,\n  },\n  titleContainer: {\n    alignItems: \"center\",\n    marginBottom: 60,\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: \"bold\",\n    color: \"#333\",\n    marginBottom: 8,\n  },\n  titleBlue: {\n    color: \"#4A90E2\",\n  },\n  subtitle: {\n    fontSize: 16,\n    color: \"#666\",\n    textAlign: \"center\",\n  },\n  loadingContainer: {\n    flexDirection: \"row\",\n    alignItems: \"center\",\n  },\n  loadingText: {\n    marginLeft: 10,\n    fontSize: 14,\n    color: \"#666\",\n  },\n})\n\nexport default SplashScreen\n"], "mappings": ";;;;;AAEA,IAAMA,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EACzB,OACEC,IAAA,CAACC,IAAI;IAACC,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EAC5BC,KAAA,CAACL,IAAI;MAACC,KAAK,EAAEC,MAAM,CAACI,OAAQ;MAAAF,QAAA,GAC1BL,IAAA,CAACC,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACK,aAAc;QAAAH,QAAA,EAChCL,IAAA,CAACC,IAAI;UAACC,KAAK,EAAEC,MAAM,CAACM,UAAW;UAAAJ,QAAA,EAC7BC,KAAA,CAACL,IAAI;YAACC,KAAK,EAAEC,MAAM,CAACO,SAAU;YAAAL,QAAA,GAC5BL,IAAA,CAACC,IAAI;cAACC,KAAK,EAAEC,MAAM,CAACQ;YAAU,CAAE,CAAC,EACjCX,IAAA,CAACC,IAAI;cAACC,KAAK,EAAEC,MAAM,CAACS;YAAK,CAAE,CAAC,EAC5BZ,IAAA,CAACC,IAAI;cAACC,KAAK,EAAEC,MAAM,CAACU;YAAK,CAAE,CAAC,EAC5Bb,IAAA,CAACC,IAAI;cAACC,KAAK,EAAEC,MAAM,CAACW;YAAK,CAAE,CAAC,EAC5Bd,IAAA,CAACC,IAAI;cAACC,KAAK,EAAEC,MAAM,CAACY;YAAK,CAAE,CAAC;UAAA,CACxB;QAAC,CACH;MAAC,CACH,CAAC,EAEPT,KAAA,CAACL,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACa,cAAe;QAAAX,QAAA,GACjCC,KAAA,CAACW,IAAI;UAACf,KAAK,EAAEC,MAAM,CAACe,KAAM;UAAAb,QAAA,GAAC,MACrB,EAAAL,IAAA,CAACiB,IAAI;YAACf,KAAK,EAAEC,MAAM,CAACgB,SAAU;YAAAd,QAAA,EAAC;UAAC,CAAM,CAAC,aAC7C;QAAA,CAAM,CAAC,EACPL,IAAA,CAACiB,IAAI;UAACf,KAAK,EAAEC,MAAM,CAACiB,QAAS;UAAAf,QAAA,EAAC;QAAqC,CAAM,CAAC;MAAA,CACtE,CAAC,EAEPC,KAAA,CAACL,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACkB,gBAAiB;QAAAhB,QAAA,GACnCL,IAAA,CAACsB,iBAAiB;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAC;QAAS,CAAE,CAAC,EAClDxB,IAAA,CAACiB,IAAI;UAACf,KAAK,EAAEC,MAAM,CAACsB,WAAY;UAAApB,QAAA,EAAC;QAA+B,CAAM,CAAC;MAAA,CACnE,CAAC;IAAA,CACH;EAAC,CACH,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGuB,UAAU,CAACC,MAAM,CAAC;EAC/BvB,SAAS,EAAE;IACTwB,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDxB,OAAO,EAAE;IACPwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE;EAClB,CAAC;EACDtB,aAAa,EAAE;IACbwB,YAAY,EAAE;EAChB,CAAC;EACDvB,UAAU,EAAE;IACVwB,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,YAAY,EAAE,EAAE;IAChBN,eAAe,EAAE,SAAS;IAC1BC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBK,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MACZJ,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDI,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE;EACb,CAAC;EACD9B,SAAS,EAAE;IACTuB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVO,QAAQ,EAAE;EACZ,CAAC;EACD9B,SAAS,EAAE;IACTsB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBO,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,OAAO;IACpBF,QAAQ,EAAE;EACZ,CAAC;EACD7B,IAAI,EAAE;IACJqB,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,CAAC;IACfN,eAAe,EAAE,OAAO;IACxBY,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE;EACR,CAAC;EACDhC,IAAI,EAAE;IACJoB,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,CAAC;IACfN,eAAe,EAAE,OAAO;IACxBY,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,EAAE;IACPE,KAAK,EAAE;EACT,CAAC;EACDhC,IAAI,EAAE;IACJmB,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,CAAC;IACfN,eAAe,EAAE,OAAO;IACxBY,QAAQ,EAAE,UAAU;IACpBM,MAAM,EAAE,EAAE;IACVF,IAAI,EAAE;EACR,CAAC;EACD9B,IAAI,EAAE;IACJkB,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,CAAC;IACfN,eAAe,EAAE,OAAO;IACxBY,QAAQ,EAAE,UAAU;IACpBM,MAAM,EAAE,EAAE;IACVD,KAAK,EAAE;EACT,CAAC;EACD9B,cAAc,EAAE;IACde,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDd,KAAK,EAAE;IACL8B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBzB,KAAK,EAAE,MAAM;IACbQ,YAAY,EAAE;EAChB,CAAC;EACDb,SAAS,EAAE;IACTK,KAAK,EAAE;EACT,CAAC;EACDJ,QAAQ,EAAE;IACR4B,QAAQ,EAAE,EAAE;IACZxB,KAAK,EAAE,MAAM;IACb0B,SAAS,EAAE;EACb,CAAC;EACD7B,gBAAgB,EAAE;IAChB8B,aAAa,EAAE,KAAK;IACpBpB,UAAU,EAAE;EACd,CAAC;EACDN,WAAW,EAAE;IACX2B,UAAU,EAAE,EAAE;IACdJ,QAAQ,EAAE,EAAE;IACZxB,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAezB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}