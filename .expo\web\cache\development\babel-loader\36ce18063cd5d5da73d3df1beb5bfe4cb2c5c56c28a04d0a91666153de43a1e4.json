{"ast": null, "code": "import backgroundClip from 'inline-style-prefixer/lib/plugins/backgroundClip';\nimport crossFade from 'inline-style-prefixer/lib/plugins/crossFade';\nimport cursor from 'inline-style-prefixer/lib/plugins/cursor';\nimport filter from 'inline-style-prefixer/lib/plugins/filter';\nimport imageSet from 'inline-style-prefixer/lib/plugins/imageSet';\nimport logical from 'inline-style-prefixer/lib/plugins/logical';\nimport position from 'inline-style-prefixer/lib/plugins/position';\nimport sizing from 'inline-style-prefixer/lib/plugins/sizing';\nimport transition from 'inline-style-prefixer/lib/plugins/transition';\nvar w = ['Webkit'];\nvar m = ['Moz'];\nvar wm = ['Webkit', 'Moz'];\nvar wms = ['Webkit', 'ms'];\nvar wmms = ['Webkit', 'Moz', 'ms'];\nexport default {\n  plugins: [backgroundClip, crossFade, cursor, filter, imageSet, logical, position, sizing, transition],\n  prefixMap: {\n    appearance: wmms,\n    userSelect: wm,\n    textEmphasisPosition: wms,\n    textEmphasis: wms,\n    textEmphasisStyle: wms,\n    textEmphasisColor: wms,\n    boxDecorationBreak: wms,\n    clipPath: w,\n    maskImage: wms,\n    maskMode: wms,\n    maskRepeat: wms,\n    maskPosition: wms,\n    maskClip: wms,\n    maskOrigin: wms,\n    maskSize: wms,\n    maskComposite: wms,\n    mask: wms,\n    maskBorderSource: wms,\n    maskBorderMode: wms,\n    maskBorderSlice: wms,\n    maskBorderWidth: wms,\n    maskBorderOutset: wms,\n    maskBorderRepeat: wms,\n    maskBorder: wms,\n    maskType: wms,\n    textDecorationStyle: w,\n    textDecorationSkip: w,\n    textDecorationLine: w,\n    textDecorationColor: w,\n    filter: w,\n    breakAfter: w,\n    breakBefore: w,\n    breakInside: w,\n    columnCount: w,\n    columnFill: w,\n    columnGap: w,\n    columnRule: w,\n    columnRuleColor: w,\n    columnRuleStyle: w,\n    columnRuleWidth: w,\n    columns: w,\n    columnSpan: w,\n    columnWidth: w,\n    backdropFilter: w,\n    hyphens: w,\n    flowInto: w,\n    flowFrom: w,\n    regionFragment: w,\n    textOrientation: w,\n    tabSize: m,\n    fontKerning: w,\n    textSizeAdjust: w\n  }\n};", "map": {"version": 3, "names": ["backgroundClip", "crossFade", "cursor", "filter", "imageSet", "logical", "position", "sizing", "transition", "w", "m", "wm", "wms", "wmms", "plugins", "prefixMap", "appearance", "userSelect", "textEmphasisPosition", "textEmphasis", "textEmphasisStyle", "textEmphasisColor", "boxDecorationBreak", "clipPath", "maskImage", "maskMode", "maskRepeat", "maskPosition", "mask<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "maskSize", "maskComposite", "mask", "maskBorderSource", "maskBorderMode", "maskBorderSlice", "maskBorderWidth", "maskBorderOutset", "maskBorderRepeat", "maskBorder", "maskType", "textDecorationStyle", "textDecorationSkip", "textDecorationLine", "textDecorationColor", "breakAfter", "breakBefore", "breakInside", "columnCount", "columnFill", "columnGap", "columnRule", "columnRuleColor", "columnRuleStyle", "columnRuleWidth", "columns", "columnSpan", "columnWidth", "<PERSON><PERSON>ilter", "hyphens", "flowInto", "flowFrom", "regionFragment", "textOrientation", "tabSize", "fontKerning", "textSizeAdjust"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/prefixStyles/static.js"], "sourcesContent": ["import backgroundClip from 'inline-style-prefixer/lib/plugins/backgroundClip';\nimport crossFade from 'inline-style-prefixer/lib/plugins/crossFade';\nimport cursor from 'inline-style-prefixer/lib/plugins/cursor';\nimport filter from 'inline-style-prefixer/lib/plugins/filter';\nimport imageSet from 'inline-style-prefixer/lib/plugins/imageSet';\nimport logical from 'inline-style-prefixer/lib/plugins/logical';\nimport position from 'inline-style-prefixer/lib/plugins/position';\nimport sizing from 'inline-style-prefixer/lib/plugins/sizing';\nimport transition from 'inline-style-prefixer/lib/plugins/transition';\nvar w = ['Webkit'];\nvar m = ['Moz'];\nvar wm = ['Webkit', 'Moz'];\nvar wms = ['Webkit', 'ms'];\nvar wmms = ['Webkit', 'Moz', 'ms'];\nexport default {\n  plugins: [backgroundClip, crossFade, cursor, filter, imageSet, logical, position, sizing, transition],\n  prefixMap: {\n    appearance: wmms,\n    userSelect: wm,\n    textEmphasisPosition: wms,\n    textEmphasis: wms,\n    textEmphasisStyle: wms,\n    textEmphasisColor: wms,\n    boxDecorationBreak: wms,\n    clipPath: w,\n    maskImage: wms,\n    maskMode: wms,\n    maskRepeat: wms,\n    maskPosition: wms,\n    maskClip: wms,\n    maskOrigin: wms,\n    maskSize: wms,\n    maskComposite: wms,\n    mask: wms,\n    maskBorderSource: wms,\n    maskBorderMode: wms,\n    maskBorderSlice: wms,\n    maskBorderWidth: wms,\n    maskBorderOutset: wms,\n    maskBorderRepeat: wms,\n    maskBorder: wms,\n    maskType: wms,\n    textDecorationStyle: w,\n    textDecorationSkip: w,\n    textDecorationLine: w,\n    textDecorationColor: w,\n    filter: w,\n    breakAfter: w,\n    breakBefore: w,\n    breakInside: w,\n    columnCount: w,\n    columnFill: w,\n    columnGap: w,\n    columnRule: w,\n    columnRuleColor: w,\n    columnRuleStyle: w,\n    columnRuleWidth: w,\n    columns: w,\n    columnSpan: w,\n    columnWidth: w,\n    backdropFilter: w,\n    hyphens: w,\n    flowInto: w,\n    flowFrom: w,\n    regionFragment: w,\n    textOrientation: w,\n    tabSize: m,\n    fontKerning: w,\n    textSizeAdjust: w\n  }\n};"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kDAAkD;AAC7E,OAAOC,SAAS,MAAM,6CAA6C;AACnE,OAAOC,MAAM,MAAM,0CAA0C;AAC7D,OAAOC,MAAM,MAAM,0CAA0C;AAC7D,OAAOC,QAAQ,MAAM,4CAA4C;AACjE,OAAOC,OAAO,MAAM,2CAA2C;AAC/D,OAAOC,QAAQ,MAAM,4CAA4C;AACjE,OAAOC,MAAM,MAAM,0CAA0C;AAC7D,OAAOC,UAAU,MAAM,8CAA8C;AACrE,IAAIC,CAAC,GAAG,CAAC,QAAQ,CAAC;AAClB,IAAIC,CAAC,GAAG,CAAC,KAAK,CAAC;AACf,IAAIC,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;AAC1B,IAAIC,GAAG,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;AAC1B,IAAIC,IAAI,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;AAClC,eAAe;EACbC,OAAO,EAAE,CAACd,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,CAAC;EACrGO,SAAS,EAAE;IACTC,UAAU,EAAEH,IAAI;IAChBI,UAAU,EAAEN,EAAE;IACdO,oBAAoB,EAAEN,GAAG;IACzBO,YAAY,EAAEP,GAAG;IACjBQ,iBAAiB,EAAER,GAAG;IACtBS,iBAAiB,EAAET,GAAG;IACtBU,kBAAkB,EAAEV,GAAG;IACvBW,QAAQ,EAAEd,CAAC;IACXe,SAAS,EAAEZ,GAAG;IACda,QAAQ,EAAEb,GAAG;IACbc,UAAU,EAAEd,GAAG;IACfe,YAAY,EAAEf,GAAG;IACjBgB,QAAQ,EAAEhB,GAAG;IACbiB,UAAU,EAAEjB,GAAG;IACfkB,QAAQ,EAAElB,GAAG;IACbmB,aAAa,EAAEnB,GAAG;IAClBoB,IAAI,EAAEpB,GAAG;IACTqB,gBAAgB,EAAErB,GAAG;IACrBsB,cAAc,EAAEtB,GAAG;IACnBuB,eAAe,EAAEvB,GAAG;IACpBwB,eAAe,EAAExB,GAAG;IACpByB,gBAAgB,EAAEzB,GAAG;IACrB0B,gBAAgB,EAAE1B,GAAG;IACrB2B,UAAU,EAAE3B,GAAG;IACf4B,QAAQ,EAAE5B,GAAG;IACb6B,mBAAmB,EAAEhC,CAAC;IACtBiC,kBAAkB,EAAEjC,CAAC;IACrBkC,kBAAkB,EAAElC,CAAC;IACrBmC,mBAAmB,EAAEnC,CAAC;IACtBN,MAAM,EAAEM,CAAC;IACToC,UAAU,EAAEpC,CAAC;IACbqC,WAAW,EAAErC,CAAC;IACdsC,WAAW,EAAEtC,CAAC;IACduC,WAAW,EAAEvC,CAAC;IACdwC,UAAU,EAAExC,CAAC;IACbyC,SAAS,EAAEzC,CAAC;IACZ0C,UAAU,EAAE1C,CAAC;IACb2C,eAAe,EAAE3C,CAAC;IAClB4C,eAAe,EAAE5C,CAAC;IAClB6C,eAAe,EAAE7C,CAAC;IAClB8C,OAAO,EAAE9C,CAAC;IACV+C,UAAU,EAAE/C,CAAC;IACbgD,WAAW,EAAEhD,CAAC;IACdiD,cAAc,EAAEjD,CAAC;IACjBkD,OAAO,EAAElD,CAAC;IACVmD,QAAQ,EAAEnD,CAAC;IACXoD,QAAQ,EAAEpD,CAAC;IACXqD,cAAc,EAAErD,CAAC;IACjBsD,eAAe,EAAEtD,CAAC;IAClBuD,OAAO,EAAEtD,CAAC;IACVuD,WAAW,EAAExD,CAAC;IACdyD,cAAc,EAAEzD;EAClB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}