{"ast": null, "code": "import canUseDOM from \"../../../modules/canUseDom\";\nexport default function createCSSStyleSheet(id, rootNode, textContent) {\n  if (canUseDOM) {\n    var root = rootNode != null ? rootNode : document;\n    var element = root.getElementById(id);\n    if (element == null) {\n      element = document.createElement('style');\n      element.setAttribute('id', id);\n      if (typeof textContent === 'string') {\n        element.appendChild(document.createTextNode(textContent));\n      }\n      if (root instanceof ShadowRoot) {\n        root.insertBefore(element, root.firstChild);\n      } else {\n        var head = root.head;\n        if (head) {\n          head.insertBefore(element, head.firstChild);\n        }\n      }\n    }\n    return element.sheet;\n  } else {\n    return null;\n  }\n}", "map": {"version": 3, "names": ["canUseDOM", "createCSSStyleSheet", "id", "rootNode", "textContent", "root", "document", "element", "getElementById", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "createTextNode", "ShadowRoot", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "head", "sheet"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/exports/StyleSheet/dom/createCSSStyleSheet.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nimport canUseDOM from '../../../modules/canUseDom';\n\n// $FlowFixMe: HTMLStyleElement is incorrectly typed - https://github.com/facebook/flow/issues/2696\nexport default function createCSSStyleSheet(id, rootNode, textContent) {\n  if (canUseDOM) {\n    var root = rootNode != null ? rootNode : document;\n    var element = root.getElementById(id);\n    if (element == null) {\n      element = document.createElement('style');\n      element.setAttribute('id', id);\n      if (typeof textContent === 'string') {\n        element.appendChild(document.createTextNode(textContent));\n      }\n      if (root instanceof ShadowRoot) {\n        root.insertBefore(element, root.firstChild);\n      } else {\n        var head = root.head;\n        if (head) {\n          head.insertBefore(element, head.firstChild);\n        }\n      }\n    }\n    // $FlowFixMe: HTMLElement is incorrectly typed\n    return element.sheet;\n  } else {\n    return null;\n  }\n}"], "mappings": "AAQA,OAAOA,SAAS;AAGhB,eAAe,SAASC,mBAAmBA,CAACC,EAAE,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EACrE,IAAIJ,SAAS,EAAE;IACb,IAAIK,IAAI,GAAGF,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGG,QAAQ;IACjD,IAAIC,OAAO,GAAGF,IAAI,CAACG,cAAc,CAACN,EAAE,CAAC;IACrC,IAAIK,OAAO,IAAI,IAAI,EAAE;MACnBA,OAAO,GAAGD,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;MACzCF,OAAO,CAACG,YAAY,CAAC,IAAI,EAAER,EAAE,CAAC;MAC9B,IAAI,OAAOE,WAAW,KAAK,QAAQ,EAAE;QACnCG,OAAO,CAACI,WAAW,CAACL,QAAQ,CAACM,cAAc,CAACR,WAAW,CAAC,CAAC;MAC3D;MACA,IAAIC,IAAI,YAAYQ,UAAU,EAAE;QAC9BR,IAAI,CAACS,YAAY,CAACP,OAAO,EAAEF,IAAI,CAACU,UAAU,CAAC;MAC7C,CAAC,MAAM;QACL,IAAIC,IAAI,GAAGX,IAAI,CAACW,IAAI;QACpB,IAAIA,IAAI,EAAE;UACRA,IAAI,CAACF,YAAY,CAACP,OAAO,EAAES,IAAI,CAACD,UAAU,CAAC;QAC7C;MACF;IACF;IAEA,OAAOR,OAAO,CAACU,KAAK;EACtB,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}