{"ast": null, "code": "var accessibilityRoleToWebRole = {\n  adjustable: 'slider',\n  button: 'button',\n  header: 'heading',\n  image: 'img',\n  imagebutton: null,\n  keyboardkey: null,\n  label: null,\n  link: 'link',\n  none: 'presentation',\n  search: 'search',\n  summary: 'region',\n  text: null\n};\nvar propsToAriaRole = function propsToAriaRole(_ref) {\n  var accessibilityRole = _ref.accessibilityRole,\n    role = _ref.role;\n  var _role = role || accessibilityRole;\n  if (_role) {\n    var inferredRole = accessibilityRoleToWebRole[_role];\n    if (inferredRole !== null) {\n      return inferredRole || _role;\n    }\n  }\n};\nexport default propsToAriaRole;", "map": {"version": 3, "names": ["accessibilityRoleToWebRole", "adjustable", "button", "header", "image", "imagebutton", "keyboardkey", "label", "link", "none", "search", "summary", "text", "propsToAriaRole", "_ref", "accessibilityRole", "role", "_role", "inferredRole"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/AccessibilityUtil/propsToAriaRole.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar accessibilityRoleToWebRole = {\n  adjustable: 'slider',\n  button: 'button',\n  header: 'heading',\n  image: 'img',\n  imagebutton: null,\n  keyboardkey: null,\n  label: null,\n  link: 'link',\n  none: 'presentation',\n  search: 'search',\n  summary: 'region',\n  text: null\n};\nvar propsToAriaRole = _ref => {\n  var accessibilityRole = _ref.accessibilityRole,\n    role = _ref.role;\n  var _role = role || accessibilityRole;\n  if (_role) {\n    var inferredRole = accessibilityRoleToWebRole[_role];\n    if (inferredRole !== null) {\n      // ignore roles that don't map to web\n      return inferredRole || _role;\n    }\n  }\n};\nexport default propsToAriaRole;"], "mappings": "AASA,IAAIA,0BAA0B,GAAG;EAC/BC,UAAU,EAAE,QAAQ;EACpBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,IAAI,EAAI;EAC5B,IAAIC,iBAAiB,GAAGD,IAAI,CAACC,iBAAiB;IAC5CC,IAAI,GAAGF,IAAI,CAACE,IAAI;EAClB,IAAIC,KAAK,GAAGD,IAAI,IAAID,iBAAiB;EACrC,IAAIE,KAAK,EAAE;IACT,IAAIC,YAAY,GAAGlB,0BAA0B,CAACiB,KAAK,CAAC;IACpD,IAAIC,YAAY,KAAK,IAAI,EAAE;MAEzB,OAAOA,YAAY,IAAID,KAAK;IAC9B;EACF;AACF,CAAC;AACD,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}