"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _regeneratorAsyncGen;
var _regenerator = require("./regenerator.js");
var _regeneratorAsyncIterator = require("./regeneratorAsyncIterator.js");
function _regeneratorAsyncGen(innerFn, outerFn, self, tryLocsList, PromiseImpl) {
  return new _regeneratorAsyncIterator.default((0, _regenerator.default)().w(innerFn, outerFn, self, tryLocsList), PromiseImpl || Promise);
}

//# sourceMappingURL=regeneratorAsyncGen.js.map
