{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar Icon = function Icon(_ref) {\n  var name = _ref.name,\n    size = _ref.size,\n    color = _ref.color;\n  var getIconSymbol = function getIconSymbol(iconName) {\n    var icons = {\n      menu: \"☰\",\n      bell: \"🔔\",\n      user: \"👤\",\n      search: \"🔍\",\n      filter: \"⚙️\",\n      sort: \"↕️\",\n      home: \"🏠\",\n      grid: \"⊞\",\n      plus: \"+\",\n      truck: \"🚛\",\n      star: \"★\",\n      \"map-pin\": \"📍\"\n    };\n    return icons[iconName] || \"?\";\n  };\n  return _jsx(View, {\n    style: [styles.icon, {\n      width: size,\n      height: size\n    }],\n    children: _jsx(Text, {\n      style: [styles.iconText, {\n        fontSize: size * 0.8,\n        color: color\n      }],\n      children: getIconSymbol(name)\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  icon: {\n    justifyContent: \"center\",\n    alignItems: \"center\"\n  },\n  iconText: {\n    textAlign: \"center\"\n  }\n});\nexport default Icon;", "map": {"version": 3, "names": ["Icon", "_ref", "name", "size", "color", "getIconSymbol", "iconName", "icons", "menu", "bell", "user", "search", "filter", "sort", "home", "grid", "plus", "truck", "star", "_jsx", "View", "style", "styles", "icon", "width", "height", "children", "Text", "iconText", "fontSize", "StyleSheet", "create", "justifyContent", "alignItems", "textAlign"], "sources": ["/Users/<USER>/Downloads/logistics-app/src/components/Icon.tsx"], "sourcesContent": ["import type React from \"react\"\nimport { View, Text, StyleSheet } from \"react-native\"\n\ninterface IconProps {\n  name: string\n  size: number\n  color: string\n}\n\nconst Icon: React.FC<IconProps> = ({ name, size, color }) => {\n  const getIconSymbol = (iconName: string) => {\n    const icons: { [key: string]: string } = {\n      menu: \"☰\",\n      bell: \"🔔\",\n      user: \"👤\",\n      search: \"🔍\",\n      filter: \"⚙️\",\n      sort: \"↕️\",\n      home: \"🏠\",\n      grid: \"⊞\",\n      plus: \"+\",\n      truck: \"🚛\",\n      star: \"★\",\n      \"map-pin\": \"📍\",\n    }\n    return icons[iconName] || \"?\"\n  }\n\n  return (\n    <View style={[styles.icon, { width: size, height: size }]}>\n      <Text style={[styles.iconText, { fontSize: size * 0.8, color }]}>{getIconSymbol(name)}</Text>\n    </View>\n  )\n}\n\nconst styles = StyleSheet.create({\n  icon: {\n    justifyContent: \"center\",\n    alignItems: \"center\",\n  },\n  iconText: {\n    textAlign: \"center\",\n  },\n})\n\nexport default Icon\n"], "mappings": ";;;;AASA,IAAMA,IAAyB,GAAG,SAA5BA,IAAyBA,CAAAC,IAAA,EAA8B;EAAA,IAAxBC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAEC,IAAI,GAAAF,IAAA,CAAJE,IAAI;IAAEC,KAAK,GAAAH,IAAA,CAALG,KAAK;EACpD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,QAAgB,EAAK;IAC1C,IAAMC,KAAgC,GAAG;MACvCC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,GAAG;MACT,SAAS,EAAE;IACb,CAAC;IACD,OAAOX,KAAK,CAACD,QAAQ,CAAC,IAAI,GAAG;EAC/B,CAAC;EAED,OACEa,IAAA,CAACC,IAAI;IAACC,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAE;MAAEC,KAAK,EAAErB,IAAI;MAAEsB,MAAM,EAAEtB;IAAK,CAAC,CAAE;IAAAuB,QAAA,EACxDP,IAAA,CAACQ,IAAI;MAACN,KAAK,EAAE,CAACC,MAAM,CAACM,QAAQ,EAAE;QAAEC,QAAQ,EAAE1B,IAAI,GAAG,GAAG;QAAEC,KAAK,EAALA;MAAM,CAAC,CAAE;MAAAsB,QAAA,EAAErB,aAAa,CAACH,IAAI;IAAC,CAAO;EAAC,CACzF,CAAC;AAEX,CAAC;AAED,IAAMoB,MAAM,GAAGQ,UAAU,CAACC,MAAM,CAAC;EAC/BR,IAAI,EAAE;IACJS,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDL,QAAQ,EAAE;IACRM,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAelC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}