{"ast": null, "code": "import { unmountComponentAtNode } from 'react-dom';\nexport default unmountComponentAtNode;", "map": {"version": 3, "names": ["unmountComponentAtNode"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/exports/unmountComponentAtNode/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport { unmountComponentAtNode } from 'react-dom';\nexport default unmountComponentAtNode;"], "mappings": "AASA,SAASA,sBAAsB,QAAQ,WAAW;AAClD,eAAeA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}