{"ast": null, "code": "'use strict';\n\nfunction clamp(min, value, max) {\n  if (value < min) {\n    return min;\n  }\n  if (value > max) {\n    return max;\n  }\n  return value;\n}\nexport default clamp;", "map": {"version": 3, "names": ["clamp", "min", "value", "max"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/Utilities/clamp.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use strict';\n\nfunction clamp(min, value, max) {\n  if (value < min) {\n    return min;\n  }\n  if (value > max) {\n    return max;\n  }\n  return value;\n}\nexport default clamp;"], "mappings": "AAUA,YAAY;;AAEZ,SAASA,KAAKA,CAACC,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAID,KAAK,GAAGD,GAAG,EAAE;IACf,OAAOA,GAAG;EACZ;EACA,IAAIC,KAAK,GAAGC,GAAG,EAAE;IACf,OAAOA,GAAG;EACZ;EACA,OAAOD,KAAK;AACd;AACA,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}