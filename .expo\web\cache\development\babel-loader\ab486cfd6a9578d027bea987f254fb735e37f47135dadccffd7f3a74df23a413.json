{"ast": null, "code": "var isDisabled = function isDisabled(props) {\n  return props.disabled || Array.isArray(props.accessibilityStates) && props.accessibilityStates.indexOf('disabled') > -1;\n};\nexport default isDisabled;", "map": {"version": 3, "names": ["isDisabled", "props", "disabled", "Array", "isArray", "accessibilityStates", "indexOf"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/AccessibilityUtil/isDisabled.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar isDisabled = props => props.disabled || Array.isArray(props.accessibilityStates) && props.accessibilityStates.indexOf('disabled') > -1;\nexport default isDisabled;"], "mappings": "AASA,IAAIA,UAAU,GAAG,SAAbA,UAAUA,CAAGC,KAAK;EAAA,OAAIA,KAAK,CAACC,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACH,KAAK,CAACI,mBAAmB,CAAC,IAAIJ,KAAK,CAACI,mBAAmB,CAACC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA;AAC1I,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}