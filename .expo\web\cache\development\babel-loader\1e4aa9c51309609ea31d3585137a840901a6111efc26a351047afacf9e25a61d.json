{"ast": null, "code": "import isUnitlessNumber from \"../unitlessNumbers\";\nfunction dangerousStyleValue(name, value, isCustomProperty) {\n  var isEmpty = value == null || typeof value === 'boolean' || value === '';\n  if (isEmpty) {\n    return '';\n  }\n  if (!isCustomProperty && typeof value === 'number' && value !== 0 && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name])) {\n    return value + 'px';\n  }\n  return ('' + value).trim();\n}\nexport default dangerousStyleValue;", "map": {"version": 3, "names": ["isUnitlessNumber", "dangerousStyleValue", "name", "value", "isCustomProperty", "isEmpty", "hasOwnProperty", "trim"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/setValueForStyles/dangerousStyleValue.js"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * From React 16.0.0\n * \n */\n\nimport isUnitlessNumber from '../unitlessNumbers';\n\n/**\n * Convert a value into the proper css writable value. The style name `name`\n * should be logical (no hyphens), as specified\n * in `CSSProperty.isUnitlessNumber`.\n *\n * @param {string} name CSS property name such as `topMargin`.\n * @param {*} value CSS property value such as `10px`.\n * @return {string} Normalized style value with dimensions applied.\n */\nfunction dangerousStyleValue(name, value, isCustomProperty) {\n  // Note that we've removed escapeTextFor<PERSON>rowser() calls here since the\n  // whole string will be escaped when the attribute is injected into\n  // the markup. If you provide unsafe user data here they can inject\n  // arbitrary CSS which may be problematic (I couldn't repro this):\n  // https://www.owasp.org/index.php/XSS_Filter_Evasion_Cheat_Sheet\n  // http://www.thespanner.co.uk/2007/11/26/ultimate-xss-css-injection/\n  // This is not an XSS hole but instead a potential CSS injection issue\n  // which has lead to a greater discussion about how we're going to\n  // trust URLs moving forward. See #2115901\n\n  var isEmpty = value == null || typeof value === 'boolean' || value === '';\n  if (isEmpty) {\n    return '';\n  }\n  if (!isCustomProperty && typeof value === 'number' && value !== 0 && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name])) {\n    return value + 'px'; // Presumes implicit 'px' suffix for unitless numbers\n  }\n  return ('' + value).trim();\n}\nexport default dangerousStyleValue;"], "mappings": "AAYA,OAAOA,gBAAgB;AAWvB,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,gBAAgB,EAAE;EAW1D,IAAIC,OAAO,GAAGF,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,EAAE;EACzE,IAAIE,OAAO,EAAE;IACX,OAAO,EAAE;EACX;EACA,IAAI,CAACD,gBAAgB,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,CAAC,IAAI,EAAEH,gBAAgB,CAACM,cAAc,CAACJ,IAAI,CAAC,IAAIF,gBAAgB,CAACE,IAAI,CAAC,CAAC,EAAE;IACvI,OAAOC,KAAK,GAAG,IAAI;EACrB;EACA,OAAO,CAAC,EAAE,GAAGA,KAAK,EAAEI,IAAI,CAAC,CAAC;AAC5B;AACA,eAAeN,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}